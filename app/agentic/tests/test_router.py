import json
import uuid
from datetime import UTC, datetime

import pytest

from app.agentic.dependencies import get_agent_service
from app.agentic.schemas import Chat<PERSON><PERSON><PERSON>, ThreadRead
from app.agentic.service import AgentService
from app.main import app
from app.workspace.dependencies import get_user_org_id


@pytest.fixture
def override_agent_service(mocker):
    mock_service = mocker.Mock(spec=AgentService)
    app.dependency_overrides[get_agent_service] = lambda: mock_service
    yield mock_service
    app.dependency_overrides.pop(get_agent_service)


@pytest.fixture
def override_user_org_id(org_id):
    app.dependency_overrides[get_user_org_id] = lambda: org_id
    yield org_id
    app.dependency_overrides.pop(get_user_org_id)


@pytest.mark.anyio
async def test_chat_stream_endpoint_success(async_client, override_agent_service):
    async def mock_sse_iterator():
        metadata_data = {"thread_id": "test-thread-123"}
        yield f"event: metadata\ndata: {json.dumps(metadata_data)}\n\n"

        data = {"content": "Test "}
        yield f"event: message\ndata: {json.dumps(data)}\n\n"

        data = {"content": "stream "}
        yield f"event: message\ndata: {json.dumps(data)}\n\n"

        data = {"content": "response"}
        yield f"event: message\ndata: {json.dumps(data)}\n\n"

    override_agent_service.process_message_stream.return_value = mock_sse_iterator()

    request_data = ChatRequest(
        message="Test stream message",
        thread_id=str(uuid.uuid4()),
        crm_account_id="test-account-123",
    )
    response = await async_client.post(
        app.url_path_for("chat_stream"), json=request_data.model_dump(mode="json")
    )

    assert response.status_code == 200
    assert response.headers["content-type"] == "text/event-stream; charset=utf-8"
    assert response.content

    override_agent_service.process_message_stream.assert_called_once_with(request_data)


@pytest.mark.anyio
async def test_get_threads_endpoint_success(async_client, override_agent_service):
    thread1 = ThreadRead(
        id=uuid.uuid4(),
        thread_id="thread-1",
        organization_member_id=uuid.uuid4(),
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )
    thread2 = ThreadRead(
        id=uuid.uuid4(),
        thread_id="thread-2",
        organization_member_id=uuid.uuid4(),
        created_at=datetime.now(UTC),
        updated_at=datetime.now(UTC),
    )

    override_agent_service.get_threads_by_org_member_id.return_value = [
        thread1,
        thread2,
    ]

    response = await async_client.get(app.url_path_for("threads"))

    assert response.status_code == 200
    threads = response.json()
    assert len(threads) == 2
    assert threads[0]["thread_id"] == "thread-1"
    assert threads[1]["thread_id"] == "thread-2"

    override_agent_service.get_threads_by_org_member_id.assert_called_once()


@pytest.mark.anyio
async def test_get_thread_history_endpoint_success(
    async_client, override_agent_service
):
    thread_id = "test-thread-123"

    mock_response = {
        "pagination": {
            "thread_id": thread_id,
            "current_page": 1,
            "page_size": 20,
            "total_messages": 2,
            "total_pages": 1,
        },
        "messages": [
            {"role": "user", "content": {"type": "text", "text": "Hello"}},
            {"role": "assistant", "content": {"type": "text", "text": "Hi there!"}},
        ],
    }

    override_agent_service.get_thread_history.return_value = mock_response

    response = await async_client.get(
        app.url_path_for("thread_history", thread_id=thread_id)
    )

    assert response.status_code == 200
    assert response.headers["content-type"] == "application/json"

    response_data = response.json()
    assert response_data["pagination"]["thread_id"] == thread_id
    assert response_data["pagination"]["current_page"] == 1
    assert response_data["pagination"]["total_messages"] == 2
    assert len(response_data["messages"]) == 2
    assert response_data["messages"][0]["role"] == "user"
    assert response_data["messages"][0]["content"]["type"] == "text"
    assert response_data["messages"][0]["content"]["text"] == "Hello"
    assert response_data["messages"][1]["role"] == "assistant"
    assert response_data["messages"][1]["content"]["type"] == "text"
    assert response_data["messages"][1]["content"]["text"] == "Hi there!"

    override_agent_service.get_thread_history.assert_called_once_with(thread_id, 1, 20)
